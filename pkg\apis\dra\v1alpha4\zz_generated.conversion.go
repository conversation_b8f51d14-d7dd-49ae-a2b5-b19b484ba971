//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1alpha4

import (
	unsafe "unsafe"

	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	v1beta1 "k8s.io/kubelet/pkg/apis/dra/v1beta1"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*Claim)(nil), (*v1beta1.Claim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_Claim_To_v1beta1_Claim(a.(*Claim), b.(*v1beta1.Claim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.Claim)(nil), (*Claim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_Claim_To_v1alpha4_Claim(a.(*v1beta1.Claim), b.(*Claim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Device)(nil), (*v1beta1.Device)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_Device_To_v1beta1_Device(a.(*Device), b.(*v1beta1.Device), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.Device)(nil), (*Device)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_Device_To_v1alpha4_Device(a.(*v1beta1.Device), b.(*Device), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodePrepareResourceResponse)(nil), (*v1beta1.NodePrepareResourceResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodePrepareResourceResponse_To_v1beta1_NodePrepareResourceResponse(a.(*NodePrepareResourceResponse), b.(*v1beta1.NodePrepareResourceResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodePrepareResourceResponse)(nil), (*NodePrepareResourceResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodePrepareResourceResponse_To_v1alpha4_NodePrepareResourceResponse(a.(*v1beta1.NodePrepareResourceResponse), b.(*NodePrepareResourceResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodePrepareResourcesRequest)(nil), (*v1beta1.NodePrepareResourcesRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodePrepareResourcesRequest_To_v1beta1_NodePrepareResourcesRequest(a.(*NodePrepareResourcesRequest), b.(*v1beta1.NodePrepareResourcesRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodePrepareResourcesRequest)(nil), (*NodePrepareResourcesRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodePrepareResourcesRequest_To_v1alpha4_NodePrepareResourcesRequest(a.(*v1beta1.NodePrepareResourcesRequest), b.(*NodePrepareResourcesRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodePrepareResourcesResponse)(nil), (*v1beta1.NodePrepareResourcesResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodePrepareResourcesResponse_To_v1beta1_NodePrepareResourcesResponse(a.(*NodePrepareResourcesResponse), b.(*v1beta1.NodePrepareResourcesResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodePrepareResourcesResponse)(nil), (*NodePrepareResourcesResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodePrepareResourcesResponse_To_v1alpha4_NodePrepareResourcesResponse(a.(*v1beta1.NodePrepareResourcesResponse), b.(*NodePrepareResourcesResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeUnprepareResourceResponse)(nil), (*v1beta1.NodeUnprepareResourceResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodeUnprepareResourceResponse_To_v1beta1_NodeUnprepareResourceResponse(a.(*NodeUnprepareResourceResponse), b.(*v1beta1.NodeUnprepareResourceResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodeUnprepareResourceResponse)(nil), (*NodeUnprepareResourceResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodeUnprepareResourceResponse_To_v1alpha4_NodeUnprepareResourceResponse(a.(*v1beta1.NodeUnprepareResourceResponse), b.(*NodeUnprepareResourceResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeUnprepareResourcesRequest)(nil), (*v1beta1.NodeUnprepareResourcesRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodeUnprepareResourcesRequest_To_v1beta1_NodeUnprepareResourcesRequest(a.(*NodeUnprepareResourcesRequest), b.(*v1beta1.NodeUnprepareResourcesRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodeUnprepareResourcesRequest)(nil), (*NodeUnprepareResourcesRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodeUnprepareResourcesRequest_To_v1alpha4_NodeUnprepareResourcesRequest(a.(*v1beta1.NodeUnprepareResourcesRequest), b.(*NodeUnprepareResourcesRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*NodeUnprepareResourcesResponse)(nil), (*v1beta1.NodeUnprepareResourcesResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha4_NodeUnprepareResourcesResponse_To_v1beta1_NodeUnprepareResourcesResponse(a.(*NodeUnprepareResourcesResponse), b.(*v1beta1.NodeUnprepareResourcesResponse), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*v1beta1.NodeUnprepareResourcesResponse)(nil), (*NodeUnprepareResourcesResponse)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1beta1_NodeUnprepareResourcesResponse_To_v1alpha4_NodeUnprepareResourcesResponse(a.(*v1beta1.NodeUnprepareResourcesResponse), b.(*NodeUnprepareResourcesResponse), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1alpha4_Claim_To_v1beta1_Claim(in *Claim, out *v1beta1.Claim, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.UID = in.UID
	out.Name = in.Name
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_Claim_To_v1beta1_Claim is an autogenerated conversion function.
func Convert_v1alpha4_Claim_To_v1beta1_Claim(in *Claim, out *v1beta1.Claim, s conversion.Scope) error {
	return autoConvert_v1alpha4_Claim_To_v1beta1_Claim(in, out, s)
}

func autoConvert_v1beta1_Claim_To_v1alpha4_Claim(in *v1beta1.Claim, out *Claim, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.UID = in.UID
	out.Name = in.Name
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_Claim_To_v1alpha4_Claim is an autogenerated conversion function.
func Convert_v1beta1_Claim_To_v1alpha4_Claim(in *v1beta1.Claim, out *Claim, s conversion.Scope) error {
	return autoConvert_v1beta1_Claim_To_v1alpha4_Claim(in, out, s)
}

func autoConvert_v1alpha4_Device_To_v1beta1_Device(in *Device, out *v1beta1.Device, s conversion.Scope) error {
	out.RequestNames = *(*[]string)(unsafe.Pointer(&in.RequestNames))
	out.PoolName = in.PoolName
	out.DeviceName = in.DeviceName
	out.CDIDeviceIDs = *(*[]string)(unsafe.Pointer(&in.CDIDeviceIDs))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_Device_To_v1beta1_Device is an autogenerated conversion function.
func Convert_v1alpha4_Device_To_v1beta1_Device(in *Device, out *v1beta1.Device, s conversion.Scope) error {
	return autoConvert_v1alpha4_Device_To_v1beta1_Device(in, out, s)
}

func autoConvert_v1beta1_Device_To_v1alpha4_Device(in *v1beta1.Device, out *Device, s conversion.Scope) error {
	out.RequestNames = *(*[]string)(unsafe.Pointer(&in.RequestNames))
	out.PoolName = in.PoolName
	out.DeviceName = in.DeviceName
	out.CDIDeviceIDs = *(*[]string)(unsafe.Pointer(&in.CDIDeviceIDs))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_Device_To_v1alpha4_Device is an autogenerated conversion function.
func Convert_v1beta1_Device_To_v1alpha4_Device(in *v1beta1.Device, out *Device, s conversion.Scope) error {
	return autoConvert_v1beta1_Device_To_v1alpha4_Device(in, out, s)
}

func autoConvert_v1alpha4_NodePrepareResourceResponse_To_v1beta1_NodePrepareResourceResponse(in *NodePrepareResourceResponse, out *v1beta1.NodePrepareResourceResponse, s conversion.Scope) error {
	out.Devices = *(*[]*v1beta1.Device)(unsafe.Pointer(&in.Devices))
	out.Error = in.Error
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodePrepareResourceResponse_To_v1beta1_NodePrepareResourceResponse is an autogenerated conversion function.
func Convert_v1alpha4_NodePrepareResourceResponse_To_v1beta1_NodePrepareResourceResponse(in *NodePrepareResourceResponse, out *v1beta1.NodePrepareResourceResponse, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodePrepareResourceResponse_To_v1beta1_NodePrepareResourceResponse(in, out, s)
}

func autoConvert_v1beta1_NodePrepareResourceResponse_To_v1alpha4_NodePrepareResourceResponse(in *v1beta1.NodePrepareResourceResponse, out *NodePrepareResourceResponse, s conversion.Scope) error {
	out.Devices = *(*[]*Device)(unsafe.Pointer(&in.Devices))
	out.Error = in.Error
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodePrepareResourceResponse_To_v1alpha4_NodePrepareResourceResponse is an autogenerated conversion function.
func Convert_v1beta1_NodePrepareResourceResponse_To_v1alpha4_NodePrepareResourceResponse(in *v1beta1.NodePrepareResourceResponse, out *NodePrepareResourceResponse, s conversion.Scope) error {
	return autoConvert_v1beta1_NodePrepareResourceResponse_To_v1alpha4_NodePrepareResourceResponse(in, out, s)
}

func autoConvert_v1alpha4_NodePrepareResourcesRequest_To_v1beta1_NodePrepareResourcesRequest(in *NodePrepareResourcesRequest, out *v1beta1.NodePrepareResourcesRequest, s conversion.Scope) error {
	out.Claims = *(*[]*v1beta1.Claim)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodePrepareResourcesRequest_To_v1beta1_NodePrepareResourcesRequest is an autogenerated conversion function.
func Convert_v1alpha4_NodePrepareResourcesRequest_To_v1beta1_NodePrepareResourcesRequest(in *NodePrepareResourcesRequest, out *v1beta1.NodePrepareResourcesRequest, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodePrepareResourcesRequest_To_v1beta1_NodePrepareResourcesRequest(in, out, s)
}

func autoConvert_v1beta1_NodePrepareResourcesRequest_To_v1alpha4_NodePrepareResourcesRequest(in *v1beta1.NodePrepareResourcesRequest, out *NodePrepareResourcesRequest, s conversion.Scope) error {
	out.Claims = *(*[]*Claim)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodePrepareResourcesRequest_To_v1alpha4_NodePrepareResourcesRequest is an autogenerated conversion function.
func Convert_v1beta1_NodePrepareResourcesRequest_To_v1alpha4_NodePrepareResourcesRequest(in *v1beta1.NodePrepareResourcesRequest, out *NodePrepareResourcesRequest, s conversion.Scope) error {
	return autoConvert_v1beta1_NodePrepareResourcesRequest_To_v1alpha4_NodePrepareResourcesRequest(in, out, s)
}

func autoConvert_v1alpha4_NodePrepareResourcesResponse_To_v1beta1_NodePrepareResourcesResponse(in *NodePrepareResourcesResponse, out *v1beta1.NodePrepareResourcesResponse, s conversion.Scope) error {
	out.Claims = *(*map[string]*v1beta1.NodePrepareResourceResponse)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodePrepareResourcesResponse_To_v1beta1_NodePrepareResourcesResponse is an autogenerated conversion function.
func Convert_v1alpha4_NodePrepareResourcesResponse_To_v1beta1_NodePrepareResourcesResponse(in *NodePrepareResourcesResponse, out *v1beta1.NodePrepareResourcesResponse, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodePrepareResourcesResponse_To_v1beta1_NodePrepareResourcesResponse(in, out, s)
}

func autoConvert_v1beta1_NodePrepareResourcesResponse_To_v1alpha4_NodePrepareResourcesResponse(in *v1beta1.NodePrepareResourcesResponse, out *NodePrepareResourcesResponse, s conversion.Scope) error {
	out.Claims = *(*map[string]*NodePrepareResourceResponse)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodePrepareResourcesResponse_To_v1alpha4_NodePrepareResourcesResponse is an autogenerated conversion function.
func Convert_v1beta1_NodePrepareResourcesResponse_To_v1alpha4_NodePrepareResourcesResponse(in *v1beta1.NodePrepareResourcesResponse, out *NodePrepareResourcesResponse, s conversion.Scope) error {
	return autoConvert_v1beta1_NodePrepareResourcesResponse_To_v1alpha4_NodePrepareResourcesResponse(in, out, s)
}

func autoConvert_v1alpha4_NodeUnprepareResourceResponse_To_v1beta1_NodeUnprepareResourceResponse(in *NodeUnprepareResourceResponse, out *v1beta1.NodeUnprepareResourceResponse, s conversion.Scope) error {
	out.Error = in.Error
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodeUnprepareResourceResponse_To_v1beta1_NodeUnprepareResourceResponse is an autogenerated conversion function.
func Convert_v1alpha4_NodeUnprepareResourceResponse_To_v1beta1_NodeUnprepareResourceResponse(in *NodeUnprepareResourceResponse, out *v1beta1.NodeUnprepareResourceResponse, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodeUnprepareResourceResponse_To_v1beta1_NodeUnprepareResourceResponse(in, out, s)
}

func autoConvert_v1beta1_NodeUnprepareResourceResponse_To_v1alpha4_NodeUnprepareResourceResponse(in *v1beta1.NodeUnprepareResourceResponse, out *NodeUnprepareResourceResponse, s conversion.Scope) error {
	out.Error = in.Error
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodeUnprepareResourceResponse_To_v1alpha4_NodeUnprepareResourceResponse is an autogenerated conversion function.
func Convert_v1beta1_NodeUnprepareResourceResponse_To_v1alpha4_NodeUnprepareResourceResponse(in *v1beta1.NodeUnprepareResourceResponse, out *NodeUnprepareResourceResponse, s conversion.Scope) error {
	return autoConvert_v1beta1_NodeUnprepareResourceResponse_To_v1alpha4_NodeUnprepareResourceResponse(in, out, s)
}

func autoConvert_v1alpha4_NodeUnprepareResourcesRequest_To_v1beta1_NodeUnprepareResourcesRequest(in *NodeUnprepareResourcesRequest, out *v1beta1.NodeUnprepareResourcesRequest, s conversion.Scope) error {
	out.Claims = *(*[]*v1beta1.Claim)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodeUnprepareResourcesRequest_To_v1beta1_NodeUnprepareResourcesRequest is an autogenerated conversion function.
func Convert_v1alpha4_NodeUnprepareResourcesRequest_To_v1beta1_NodeUnprepareResourcesRequest(in *NodeUnprepareResourcesRequest, out *v1beta1.NodeUnprepareResourcesRequest, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodeUnprepareResourcesRequest_To_v1beta1_NodeUnprepareResourcesRequest(in, out, s)
}

func autoConvert_v1beta1_NodeUnprepareResourcesRequest_To_v1alpha4_NodeUnprepareResourcesRequest(in *v1beta1.NodeUnprepareResourcesRequest, out *NodeUnprepareResourcesRequest, s conversion.Scope) error {
	out.Claims = *(*[]*Claim)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodeUnprepareResourcesRequest_To_v1alpha4_NodeUnprepareResourcesRequest is an autogenerated conversion function.
func Convert_v1beta1_NodeUnprepareResourcesRequest_To_v1alpha4_NodeUnprepareResourcesRequest(in *v1beta1.NodeUnprepareResourcesRequest, out *NodeUnprepareResourcesRequest, s conversion.Scope) error {
	return autoConvert_v1beta1_NodeUnprepareResourcesRequest_To_v1alpha4_NodeUnprepareResourcesRequest(in, out, s)
}

func autoConvert_v1alpha4_NodeUnprepareResourcesResponse_To_v1beta1_NodeUnprepareResourcesResponse(in *NodeUnprepareResourcesResponse, out *v1beta1.NodeUnprepareResourcesResponse, s conversion.Scope) error {
	out.Claims = *(*map[string]*v1beta1.NodeUnprepareResourceResponse)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1alpha4_NodeUnprepareResourcesResponse_To_v1beta1_NodeUnprepareResourcesResponse is an autogenerated conversion function.
func Convert_v1alpha4_NodeUnprepareResourcesResponse_To_v1beta1_NodeUnprepareResourcesResponse(in *NodeUnprepareResourcesResponse, out *v1beta1.NodeUnprepareResourcesResponse, s conversion.Scope) error {
	return autoConvert_v1alpha4_NodeUnprepareResourcesResponse_To_v1beta1_NodeUnprepareResourcesResponse(in, out, s)
}

func autoConvert_v1beta1_NodeUnprepareResourcesResponse_To_v1alpha4_NodeUnprepareResourcesResponse(in *v1beta1.NodeUnprepareResourcesResponse, out *NodeUnprepareResourcesResponse, s conversion.Scope) error {
	out.Claims = *(*map[string]*NodeUnprepareResourceResponse)(unsafe.Pointer(&in.Claims))
	out.XXX_NoUnkeyedLiteral = in.XXX_NoUnkeyedLiteral
	out.XXX_sizecache = in.XXX_sizecache
	return nil
}

// Convert_v1beta1_NodeUnprepareResourcesResponse_To_v1alpha4_NodeUnprepareResourcesResponse is an autogenerated conversion function.
func Convert_v1beta1_NodeUnprepareResourcesResponse_To_v1alpha4_NodeUnprepareResourcesResponse(in *v1beta1.NodeUnprepareResourcesResponse, out *NodeUnprepareResourcesResponse, s conversion.Scope) error {
	return autoConvert_v1beta1_NodeUnprepareResourcesResponse_To_v1alpha4_NodeUnprepareResourcesResponse(in, out, s)
}
